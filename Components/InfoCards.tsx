import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Typography from './Typography';
import { Colors, Spacing, Shadow } from '../theme';
import { Grade, Ingredient, Effects, InfoCard } from '../context/types';

/**
 * InfoCards Component
 *
 * Displays 4 dynamic information cards in a 2x2 grid layout that replace the rating justification section.
 * Cards show the most useful product information as determined by AI analysis:
 *
 * - Cards are dynamically generated by the AI based on product type and context
 * - Content includes relevant metrics like calories, protein, SPF, allergen alerts, etc.
 * - Colors and icons are chosen by AI to indicate safety levels and importance
 * - Prioritizes critical information like allergy warnings and safety concerns
 *
 * The AI determines what information would be most valuable for each specific product.
 */

interface InfoCardsProps {
  apiResponse: any;
  grade: Grade;
  goodIngredients: Ingredient[];
  badIngredients: Ingredient[];
  effects?: Effects;
  productName: string;
  savedInfoCards?: InfoCard[]; // For history items
}

// Use the InfoCard interface from types
type CardData = InfoCard;

// Helper function to get background color from main color
const getBackgroundColor = (color: string): string => {
  // Handle undefined or null colors
  if (!color) {
    return 'rgba(0, 0, 0, 0.05)';
  }

  // Convert hex color to rgba with low opacity
  if (color.startsWith('#') && color.length >= 7) {
    const hex = color.slice(1);
    const r = parseInt(hex.substring(0, 2), 16) || 0;
    const g = parseInt(hex.substring(2, 4), 16) || 0;
    const b = parseInt(hex.substring(4, 6), 16) || 0;
    return `rgba(${r}, ${g}, ${b}, 0.1)`;
  }

  // Handle common color names
  const colorMap: { [key: string]: string } = {
    red: 'rgba(255, 0, 0, 0.1)',
    green: 'rgba(0, 204, 0, 0.1)',
    blue: 'rgba(0, 122, 255, 0.1)',
    orange: 'rgba(255, 165, 0, 0.1)',
    yellow: 'rgba(255, 215, 0, 0.1)',
  };

  const lowerColor = color.toLowerCase();
  if (colorMap[lowerColor]) {
    return colorMap[lowerColor];
  }

  // Fallback
  return 'rgba(0, 0, 0, 0.05)';
};

const InfoCards: React.FC<InfoCardsProps> = ({
  apiResponse,
  grade,
  goodIngredients = [],
  badIngredients = [],
  effects,
  productName = 'Unknown Product',
  savedInfoCards,
}) => {
  // ENHANCED: Add logging to verify InfoCards data persistence
  console.log('InfoCards: Component rendered with data:', {
    productName,
    hasSavedInfoCards: !!savedInfoCards,
    savedInfoCardsCount: savedInfoCards?.length || 0,
    savedInfoCardsData: savedInfoCards,
    hasApiResponse: !!apiResponse,
    apiInfoCards: apiResponse?.info_cards,
    apiInfoCardsCount: apiResponse?.info_cards?.length || 0
  });

  // Note: Removed getProductType as we now use fixed Calories + Protein system

  // Create fixed mandatory cards - ALWAYS Calories + Protein for consistency
  const getMandatoryCards = (): CardData[] => {
    // If we have saved cards from history, look for Calories and Protein cards specifically
    if (savedInfoCards && savedInfoCards.length > 0) {
      const caloriesCard = savedInfoCards.find(card =>
        card.title.toLowerCase().includes('calorie') || card.title.toLowerCase() === 'calories'
      );
      const proteinCard = savedInfoCards.find(card =>
        card.title.toLowerCase().includes('protein')
      );

      // If we found both mandatory cards in saved data, use them
      if (caloriesCard && proteinCard) {
        return [caloriesCard, proteinCard];
      }
    }

    // FIXED SYSTEM: Always show Calories + Protein as first 2 cards
    // This provides consistency across all products
    return [
      {
        title: 'Calories',
        value: 'See Package',
        icon: 'flame',
        color: '#FF6B35',
        priority: 'high'
      },
      {
        title: 'Protein',
        value: 'Check Label',
        icon: 'fitness',
        color: '#00CC00',
        priority: 'high'
      }
    ];
  };

  // Get smart cards from saved data, AI, or create fallback
  const getSmartCards = (): CardData[] => {
    // First priority: Use saved cards from history (exclude mandatory cards)
    if (savedInfoCards && savedInfoCards.length > 0) {
      // Filter out mandatory cards (Calories and Protein) to get the dynamic cards
      const dynamicCards = savedInfoCards.filter(card => {
        const title = card.title.toLowerCase();
        return !title.includes('calorie') &&
               !title.includes('protein') &&
               title !== 'calories';
      });

      // If we have dynamic cards from saved data, use up to 2 of them
      if (dynamicCards.length > 0) {
        return dynamicCards.slice(0, 2);
      }
    }

    // Second priority: Try to get AI-generated cards from API response
    if (apiResponse?.info_cards && Array.isArray(apiResponse.info_cards) && apiResponse.info_cards.length > 0) {
      // Filter out mandatory cards from API response to get dynamic cards
      const apiDynamicCards = apiResponse.info_cards.filter((card: any) => {
        const title = card.title?.toLowerCase() || '';
        return !title.includes('calorie') &&
               !title.includes('protein') &&
               title !== 'calories';
      });

      // Use up to 2 dynamic cards from API
      if (apiDynamicCards.length > 0) {
        return apiDynamicCards.slice(0, 2);
      }
    }

    // Fallback smart cards
    const safeGrade = grade || 'C';
    const getGradeColor = (grade: Grade): string => {
      switch (grade) {
        case 'A': return Colors.GradeA;
        case 'B': return Colors.GradeB;
        case 'C': return Colors.GradeC;
        case 'D': return Colors.GradeD;
        case 'E': return Colors.GradeE;
        default: return Colors.GradeC;
      }
    };

    return [
      {
        title: 'Overall Grade',
        value: safeGrade,
        icon: 'star',
        color: getGradeColor(safeGrade),
        priority: 'medium'
      },
      {
        title: 'Concerns',
        value: badIngredients.length.toString(),
        icon: 'alert-circle',
        color: badIngredients.length > 0 ? Colors.Error : Colors.Success,
        priority: 'medium'
      }
    ];
  };

  // Combine mandatory + smart cards to ensure exactly 4 cards
  const getCardData = (): CardData[] => {
    const mandatoryCards = getMandatoryCards(); // Always 2 cards
    const smartCards = getSmartCards(); // Up to 2 cards

    // Combine cards
    const allCards = [...mandatoryCards, ...smartCards];

    // Ensure we have exactly 4 cards by adding fallback cards if needed
    while (allCards.length < 4) {
      const safeGrade = grade || 'C';
      const getGradeColor = (grade: Grade): string => {
        switch (grade) {
          case 'A': return Colors.GradeA;
          case 'B': return Colors.GradeB;
          case 'C': return Colors.GradeC;
          case 'D': return Colors.GradeD;
          case 'E': return Colors.GradeE;
          default: return Colors.GradeC;
        }
      };

      if (allCards.length === 2) {
        // Add Overall Grade as 3rd card
        allCards.push({
          title: 'Overall Grade',
          value: safeGrade,
          icon: 'star',
          color: getGradeColor(safeGrade),
          priority: 'medium'
        });
      } else if (allCards.length === 3) {
        // Add Concerns as 4th card
        allCards.push({
          title: 'Concerns',
          value: badIngredients.length.toString(),
          icon: 'alert-circle',
          color: badIngredients.length > 0 ? Colors.Error : Colors.Success,
          priority: 'medium'
        });
      }
    }

    // Return exactly 4 cards
    return allCards.slice(0, 4);
  };

  const cardData = getCardData();

  // ENHANCED: Log final card data for verification
  console.log('InfoCards: Final card data:', {
    productName,
    cardCount: cardData.length,
    cards: cardData.map(card => ({
      title: card.title,
      value: card.value,
      icon: card.icon,
      color: card.color,
      priority: card.priority
    }))
  });

  const renderIcon = (iconName: string, color: string) => {
    // Fallback icon if iconName is invalid
    const safeIconName = iconName || 'information-circle';
    const safeColor = color || Colors.DarkText;

    try {
      return <Ionicons name={safeIconName as any} size={20} color={safeColor} />;
    } catch (error) {
      // Fallback to a safe icon if the provided icon name is invalid
      return <Ionicons name="information-circle" size={20} color={safeColor} />;
    }
  };

  const renderCard = (card: CardData, index: number) => {
    // Ensure we have valid color values
    const cardColor = card.color || Colors.DarkText;
    const iconBackgroundColor = getBackgroundColor(cardColor);

    // Determine if this is a high priority/warning card
    const isHighPriority = card.priority === 'high' || cardColor === '#FF0000' || cardColor === Colors.Error;
    const isWarning = cardColor === '#FF0000' || cardColor === Colors.Error;

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.card,
          Shadow.Small,
          isHighPriority && {
            borderColor: cardColor,
            borderWidth: 1.5,
            backgroundColor: isWarning ? 'rgba(255, 0, 0, 0.02)' : Colors.BackgroundPrimary
          }
        ]}
        activeOpacity={0.95}
        onPress={() => {
          // Could add haptic feedback or detailed view in the future
        }}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
              {renderIcon(card.icon, cardColor)}
            </View>
          </View>
          <View style={styles.cardInfo}>
            <Typography variant="caption" style={styles.cardTitle}>
              {card.title}
            </Typography>
            <Typography variant="heading2" style={{...styles.cardValue, color: cardColor}}>
              {card.value}
            </Typography>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Section Header */}
      <View style={styles.sectionHeader}>
        <View style={[styles.headerIconContainer, { backgroundColor: Colors.SurfaceSecondary }]}>
          <Ionicons name="analytics-outline" size={18} color={Colors.DarkText} />
        </View>
        <Typography variant="heading2" style={styles.sectionTitle}>
          Nutrition & Key Info
        </Typography>
      </View>

      {/* Cards Grid */}
      <View style={styles.cardsGrid}>
        {cardData.map((card, index) => renderCard(card, index))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.Large,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  headerIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.Small,
    backgroundColor: Colors.SurfaceSecondary,
  },
  sectionTitle: {
    marginLeft: Spacing.Small,
    fontWeight: '600',
    color: Colors.DarkText,
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  card: {
    width: '48%',
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 20,
    marginBottom: Spacing.Medium,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.06)',
    overflow: 'hidden',
  },
  cardContent: {
    padding: Spacing.Medium,
    alignItems: 'flex-start',
    minHeight: 110,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Small,
    justifyContent: 'flex-start',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardTitle: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.LightText,
    textTransform: 'uppercase',
    letterSpacing: 0.8,
    marginBottom: 4,
  },
  cardInfo: {
    flex: 1,
  },
  cardValue: {
    fontSize: 22,
    fontWeight: '700',
    lineHeight: 26,
    letterSpacing: -0.3,
    flexShrink: 1,
  },
});

export default InfoCards;
