import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { HistoryState, ScanResult } from './types';
import {
  STORAGE_KEYS,
  storeData,
  getData,
  storeAuthAwareData,
  getAuthAwareData
} from './storage';
import { useAuth } from './AuthContext';
import { useScan } from './ScanContext';
import { isDuplicateScan } from './utils/duplicateDetection';

// Default history state
const defaultHistoryState: HistoryState = {
  scans: [],
  isLoading: false,
  error: null,
};

// Create the context
export const HistoryContext = createContext<{
  historyState: HistoryState;
  addScanToHistory: (scan: ScanResult) => Promise<void>;
  removeScanFromHistory: (scanId: string) => Promise<void>;
  removeMultipleScansFromHistory: (scanIds: string[]) => Promise<void>;
  clearHistory: () => Promise<void>;
  clearError: () => void;
}>({
  historyState: defaultHistoryState,
  addScanToHistory: async () => {},
  removeScanFromHistory: async () => {},
  removeMultipleScansFromHistory: async () => {},
  clearHistory: async () => {},
  clearError: () => {},
});

// Custom hook to use the history context
export const useHistory = () => useContext(HistoryContext);

// Provider component
export const HistoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [historyState, setHistoryState] = useState<HistoryState>(defaultHistoryState);
  const { authState } = useAuth();
  const { scanState } = useScan();

  // Generate a unique ID for scan results - memoized
  const generateUniqueId = useCallback(() => {
    return `scan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }, []);

  // Helper function to check if a scan already exists in history (using utility function)
  const checkDuplicateScan = useCallback((newScan: ScanResult, existingScans: ScanResult[]): boolean => {
    return isDuplicateScan(newScan, existingScans);
  }, []);

  // Load history from storage on mount and when auth state changes
  useEffect(() => {
    const loadHistory = async () => {
      try {
        setHistoryState({ ...historyState, isLoading: true });

        // Use authentication-aware storage
        const storedHistory = await getAuthAwareData<HistoryState>(
          authState.isAuthenticated,
          STORAGE_KEYS.ANONYMOUS.HISTORY,
          STORAGE_KEYS.AUTHENTICATED.HISTORY
        );

        if (storedHistory) {
          // ENHANCED: Add logging to verify data retrieval
          console.log('HistoryContext: Loaded history from storage:', {
            scanCount: storedHistory.scans.length,
            isAuthenticated: authState.isAuthenticated,
            scansWithEffects: storedHistory.scans.filter(scan => !!scan.effects).length,
            scansWithInfoCards: storedHistory.scans.filter(scan => !!scan.infoCards).length
          });

          // Log detailed data for first scan if available
          if (storedHistory.scans.length > 0) {
            const firstScan = storedHistory.scans[0];
            console.log('HistoryContext: First scan data verification:', {
              productName: firstScan.productName,
              hasEffects: !!firstScan.effects,
              effectsData: firstScan.effects,
              hasInfoCards: !!firstScan.infoCards,
              infoCardsCount: firstScan.infoCards?.length || 0,
              infoCardsData: firstScan.infoCards
            });
          }

          // Simply load the stored history without any duplicate checking
          setHistoryState({
            ...storedHistory,
            isLoading: false,
          });
        } else {
          console.log('HistoryContext: No stored history found, using default state');
          setHistoryState({
            ...defaultHistoryState,
            isLoading: false,
          });
        }
      } catch (error) {
        console.error('Error loading history:', error);
        setHistoryState({
          ...defaultHistoryState,
          isLoading: false,
          error: 'Failed to load scan history.',
        });
      }
    };

    loadHistory();
  }, [authState.isAuthenticated]); // Reload when auth state changes

  // Add scan to history function - memoized with duplicate replacement
  const addScanToHistory = useCallback(async (scan: ScanResult): Promise<void> => {
    try {
      // ENHANCED: Add logging to verify data persistence
      console.log('HistoryContext: Adding scan to history with data:', {
        productName: scan.productName,
        hasEffects: !!scan.effects,
        effectsData: scan.effects,
        hasInfoCards: !!scan.infoCards,
        infoCardsCount: scan.infoCards?.length || 0,
        infoCardsData: scan.infoCards,
        isAuthenticated: authState.isAuthenticated,
        timestamp: scan.timestamp
      });

      // Check if this scan already exists in history
      const existingDuplicateIndex = historyState.scans.findIndex(existingScan => {
        // Check for duplicate based on product name (case-insensitive)
        return existingScan.productName.toLowerCase() === scan.productName.toLowerCase();
      });

      let updatedScans: ScanResult[];

      if (existingDuplicateIndex !== -1) {
        // Remove the old scan and add the new one
        updatedScans = [...historyState.scans];
        updatedScans.splice(existingDuplicateIndex, 1); // Remove old scan
        updatedScans.unshift(scan); // Add new scan at the beginning
        console.log('HistoryContext: Replaced duplicate scan for product:', scan.productName);
      } else {
        // No duplicate found, add normally
        updatedScans = [scan, ...historyState.scans];
        console.log('HistoryContext: Added new scan for product:', scan.productName);
      }

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      console.log('HistoryContext: Successfully saved history state with', updatedScans.length, 'scans');

      // ENHANCED: Verify data integrity after save
      setTimeout(async () => {
        try {
          const verificationData = await getAuthAwareData<HistoryState>(
            authState.isAuthenticated,
            STORAGE_KEYS.ANONYMOUS.HISTORY,
            STORAGE_KEYS.AUTHENTICATED.HISTORY
          );

          if (verificationData) {
            const savedScan = verificationData.scans.find(s => s.id === scan.id);
            if (savedScan) {
              console.log('HistoryContext: Data integrity verification passed for scan:', {
                productName: savedScan.productName,
                hasEffects: !!savedScan.effects,
                hasInfoCards: !!savedScan.infoCards,
                infoCardsCount: savedScan.infoCards?.length || 0,
                effectsIntact: JSON.stringify(savedScan.effects) === JSON.stringify(scan.effects),
                infoCardsIntact: JSON.stringify(savedScan.infoCards) === JSON.stringify(scan.infoCards)
              });
            } else {
              console.warn('HistoryContext: Data integrity verification failed - scan not found after save');
            }
          } else {
            console.warn('HistoryContext: Data integrity verification failed - no data found after save');
          }
        } catch (error) {
          console.error('HistoryContext: Data integrity verification error:', error);
        }
      }, 100); // Small delay to ensure storage operation completes

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error adding scan to history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to add scan to history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Remove scan from history function - memoized
  const removeScanFromHistory = useCallback(async (scanId: string): Promise<void> => {
    try {
      // Remove scan from history
      const updatedScans = historyState.scans.filter(scan => scan.id !== scanId);

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error removing scan from history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to remove scan from history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Remove multiple scans from history function - memoized
  const removeMultipleScansFromHistory = useCallback(async (scanIds: string[]): Promise<void> => {
    try {
      // Remove all specified scans from history in one operation
      const updatedScans = historyState.scans.filter(scan => !scanIds.includes(scan.id));

      const newState: HistoryState = {
        scans: updatedScans,
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error removing multiple scans from history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to remove scans from history.',
      }));
    }
  }, [historyState, authState.isAuthenticated]);

  // Clear history function - memoized
  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      const newState: HistoryState = {
        scans: [],
        isLoading: false,
        error: null,
      };

      // Save to authentication-aware storage
      await storeAuthAwareData(
        authState.isAuthenticated,
        STORAGE_KEYS.ANONYMOUS.HISTORY,
        STORAGE_KEYS.AUTHENTICATED.HISTORY,
        newState
      );

      // Update state
      setHistoryState(newState);
    } catch (error) {
      console.error('Error clearing history:', error);
      setHistoryState(prevState => ({
        ...prevState,
        error: 'Failed to clear history.',
      }));
    }
  }, [authState.isAuthenticated]);

  // Clear error function - memoized
  const clearError = useCallback(() => {
    setHistoryState(prevState => ({ ...prevState, error: null }));
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    historyState,
    addScanToHistory,
    removeScanFromHistory,
    removeMultipleScansFromHistory,
    clearHistory,
    clearError,
  }), [historyState, addScanToHistory, removeScanFromHistory, removeMultipleScansFromHistory, clearHistory, clearError]);

  return (
    <HistoryContext.Provider value={contextValue}>
      {children}
    </HistoryContext.Provider>
  );
};
